<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AA.Law | Motor Vehicle Accident Attorney | Free Consultation | No Fees Unless We Win</title>
    <meta name="description" content="Injured in a motor vehicle accident? Get experienced legal help. Free consultation, no fees unless we win. Available 24/7 for accident victims.">

    <!-- Font Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Roboto+Condensed:wght@300;400;700&family=League+Gothic&display=swap" rel="stylesheet">

    <!-- Aeonik Font from CDN with specific weights -->
    <link href="https://fonts.cdnfonts.com/css/aeonik" rel="stylesheet">

    <!-- Additional Aeonik font loading to ensure Regular weight -->
    <style>
        @import url('https://fonts.cdnfonts.com/css/aeonik');

        @font-face {
            font-family: 'Aeonik';
            src: url('https://fonts.cdnfonts.com/s/19990/AeonikTRIAL-Regular.woff') format('woff');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
    </style>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'aeonik': ['Aeonik', 'Inter', 'SF Pro Display', 'Helvetica Neue', 'Arial', 'sans-serif'],
                        'roboto-condensed': ['Roboto Condensed', 'Arial Narrow', 'sans-serif'],
                        'league-gothic': ['League Gothic', 'Impact', 'Arial Narrow', 'sans-serif'],
                    },
                    colors: {
                        'primary-green': '#00a475',
                        'secondary-green': '#00c48c',
                        'vibrant-green': '#00e6a3',
                        'light-green': '#e6fff5',
                        'deep-black': '#000000',
                        'charcoal': '#1a1a1a',
                        'light-gray': '#f8f9fa',
                        'accent-orange': '#ff6b35',
                        'form-bg': '#ffffff',
                        'page-bg': '#fffffb',
                        'form-border': '#00a475',
                    },
                    spacing: {
                        '18': '4.5rem',
                        '22': '5.5rem',
                        '26': '6.5rem',
                        '30': '7.5rem',
                        '34': '8.5rem',
                        '38': '9.5rem',
                        '42': '10.5rem',
                        '46': '11.5rem',
                        '50': '12.5rem',
                        '128': '32rem',
                        '144': '36rem',
                        '160': '40rem',
                        '192': '48rem',
                    },
                    fontSize: {
                        '5xl': ['3rem', { lineHeight: '1.1' }],
                        '6xl': ['3.75rem', { lineHeight: '1.1' }],
                        '7xl': ['4.5rem', { lineHeight: '1.1' }],
                        '8xl': ['6rem', { lineHeight: '1.1' }],
                        '9xl': ['8rem', { lineHeight: '1.1' }],
                    },
                    maxWidth: {
                        '8xl': '90rem',
                        '9xl': '100rem',
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS for Gretta-Inspired Architectural Design -->
    <style>
        * {
            font-family: 'Aeonik', 'Inter', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif !important;
            font-weight: 400 !important; /* Force Regular weight */
        }

        /* Ensure Aeonik Regular is used for all text elements */
        body, h1, h2, h3, h4, h5, h6, p, span, div, a, button {
            font-family: 'Aeonik', 'Inter', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif !important;
            font-weight: 400 !important;
        }

        /* Override any font-weight classes that might interfere */
        .font-light {
            font-weight: 400 !important;
        }

        .font-normal {
            font-weight: 400 !important;
        }

        .font-medium {
            font-weight: 400 !important;
        }

        .font-semibold {
            font-weight: 400 !important;
        }

        .font-bold {
            font-weight: 400 !important;
        }

        /* Hero headline should be bold */
        .hero-headline-bold {
            font-weight: 900 !important;
        }

        .attorneys-underline {
            position: relative;
        }

        .attorneys-underline::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 120%;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, #00a475 20%, #008a63 50%, #00a475 80%, transparent 100%);
            border-radius: 1px;
            opacity: 0.8;
        }



        .architectural-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 0;
        }

        .hero-overlay {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 100%);
        }

        .geometric-button {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            border-radius: 50px;
            position: relative;
            overflow: hidden;
        }

        .geometric-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .geometric-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .geometric-button:hover::before {
            left: 100%;
        }

        /* 3D Photorealistic Button */
        .photorealistic-button {
            background: linear-gradient(145deg, #008a63, #007355);
            border: 3px solid rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            box-shadow: 
                0 8px 20px rgba(0, 0, 0, 0.15),
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                inset 0 -2px 0 rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
            color: #ffffff !important;
        }

        .photorealistic-button:hover {
            transform: translateY(-2px);
            background: linear-gradient(145deg, #009970, #008060);
            box-shadow: 
                0 12px 28px rgba(0, 0, 0, 0.2),
                0 6px 16px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.4),
                inset 0 -2px 0 rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.3);
        }

        .photorealistic-button:active {
            transform: translateY(0px);
            background: linear-gradient(145deg, #007355, #006048);
            box-shadow: 
                0 4px 12px rgba(0, 0, 0, 0.2),
                0 2px 8px rgba(0, 0, 0, 0.15),
                inset 0 2px 4px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
        }

        .photorealistic-button::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            height: 50%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
            border-radius: 50% 50% 0 0;
            pointer-events: none;
        }

        .photorealistic-button::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
            transform: rotate(-45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .photorealistic-button:hover::after {
            opacity: 1;
            transform: rotate(-45deg) translate(50%, 50%);
        }

        /* Override for menu button to keep pill shape */
        .photorealistic-button.menu-button {
            border-radius: 60px;
        }

        .photorealistic-button.menu-button::before {
            border-radius: 60px 60px 0 0;
        }

        .architectural-section {
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .service-arrow {
            transition: all 0.3s ease;
            font-size: 24px;
            font-weight: 300;
        }

        .service-item:hover .service-arrow {
            transform: translateX(12px) scale(1.1);
        }

        .smooth-scroll {
            scroll-behavior: smooth;
        }

        /* Gretta-Style Typography */
        .hero-headline {
            font-weight: 300;
            letter-spacing: -0.03em;
            line-height: 0.85;
            font-size: clamp(3rem, 8vw, 8rem);
        }

        .hero-headline .accent {
            font-weight: 700;
            font-style: italic;
        }

        .section-headline {
            font-weight: 300;
            letter-spacing: -0.02em;
            line-height: 0.9;
            font-size: clamp(2.5rem, 6vw, 5rem);
        }

        .section-headline .accent {
            font-weight: 600;
            font-style: italic;
        }

        .body-text {
            font-weight: 300;
            line-height: 1.7;
            font-size: 18px;
        }

        .menu-text {
            font-weight: 300;
            font-size: clamp(1.5rem, 3vw, 2.5rem);
            letter-spacing: -0.01em;
        }

        /* Gretta-Style Service Menu */
        .gretta-menu {
            background: #00583f;
            position: relative;
        }

        .gretta-menu::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0,0,0,0.05) 0%, rgba(0,0,0,0.1) 100%);
        }

        .service-item {
            border-bottom: 1px solid rgba(255,255,255,0.15);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .service-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.08);
            transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .service-item:hover::before {
            left: 0;
        }

        .service-item:last-child {
            border-bottom: none;
        }

        .service-number {
            font-family: 'Aeonik', 'Courier New', monospace !important;
            font-weight: 400 !important;
            opacity: 0.6;
            font-size: 14px;
        }

        /* Neumorphism Styles */
        .neumorphic-container {
            background: #e6e6e6;
            border-radius: 20px;
            box-shadow:
                20px 20px 40px rgba(0, 0, 0, 0.1),
                -20px -20px 40px rgba(255, 255, 255, 0.8);
            padding: 40px;
            margin: 20px;
        }

        .neumorphic-card {
            background: #e6e6e6;
            border-radius: 20px;
            box-shadow:
                inset 8px 8px 16px rgba(0, 0, 0, 0.1),
                inset -8px -8px 16px rgba(255, 255, 255, 0.8);
            padding: 30px 25px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .neumorphic-card:hover {
            box-shadow:
                inset 12px 12px 24px rgba(0, 0, 0, 0.15),
                inset -12px -12px 24px rgba(255, 255, 255, 0.9);
        }

        .neumorphic-icon {
            background: #e6e6e6;
            border-radius: 50%;
            box-shadow:
                8px 8px 16px rgba(0, 0, 0, 0.1),
                -8px -8px 16px rgba(255, 255, 255, 0.8);
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
        }

        .neumorphic-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: #e6e6e6;
            border-radius: 50%;
            box-shadow:
                inset 4px 4px 8px rgba(0, 0, 0, 0.1),
                inset -4px -4px 8px rgba(255, 255, 255, 0.8);
        }

        .neumorphic-icon svg,
        .neumorphic-icon i {
            position: relative;
            z-index: 1;
            color: #333;
            font-size: 24px;
        }

        .benefits-section {
            background: #e6e6e6;
            min-height: 100vh;
            padding: 80px 0;
        }

        .benefits-title {
            font-size: 3rem;
            font-weight: 400;
            color: #333;
            text-align: center;
            margin-bottom: 20px;
        }

        .benefits-subtitle {
            font-size: 1.1rem;
            color: #666;
            text-align: center;
            margin-bottom: 60px;
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .benefit-card-title {
            font-size: 1.4rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .benefit-card-description {
            font-size: 0.95rem;
            color: #666;
            line-height: 1.6;
            text-align: center;
        }

        .benefits-tags {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin-top: 50px;
            max-width: 1000px;
            margin-left: auto;
            margin-right: auto;
        }

        .benefit-tag {
            background: #e6e6e6;
            border-radius: 25px;
            box-shadow:
                6px 6px 12px rgba(0, 0, 0, 0.1),
                -6px -6px 12px rgba(255, 255, 255, 0.8);
            padding: 12px 24px;
            font-size: 0.9rem;
            color: #555;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .benefit-tag:hover {
            box-shadow:
                inset 6px 6px 12px rgba(0, 0, 0, 0.1),
                inset -6px -6px 12px rgba(255, 255, 255, 0.8);
        }

        .benefit-tag-icon {
            width: 16px;
            height: 16px;
            color: #00583f;
        }

        /* Mobile-specific styles */
        @media (max-width: 768px) {
            .service-item {
                padding: 24px 16px !important;
            }

            .service-item .flex {
                flex-direction: column !important;
                align-items: flex-start !important;
                text-align: left !important;
            }

            .service-item .flex.items-center {
                align-items: flex-start !important;
                margin-bottom: 16px;
            }

            .service-item .flex.items-center.space-x-12 {
                flex-direction: column !important;
                align-items: flex-start !important;
                gap: 8px !important;
                margin-right: 0 !important;
                margin-bottom: 16px;
            }

            .service-number {
                font-size: 16px !important;
                margin-bottom: 8px;
                display: block;
            }

            .menu-text {
                font-size: 1.5rem !important;
                line-height: 1.2 !important;
                text-align: left !important;
                white-space: pre-line;
                margin-bottom: 0;
            }

            .service-arrow {
                align-self: flex-end !important;
                margin-top: -40px;
                font-size: 20px !important;
            }

            /* Override space-x-12 on mobile */
            .space-x-12 > * + * {
                margin-left: 0 !important;
            }

            .benefits-title {
                font-size: 2.2rem;
            }

            .benefits-grid {
                grid-template-columns: 1fr;
                gap: 25px;
                padding: 0 15px;
            }

            .neumorphic-card {
                padding: 25px 20px;
            }

            .benefits-tags {
                gap: 10px;
            }

            .benefit-tag {
                padding: 10px 18px;
                font-size: 0.85rem;
            }

            /* Horizontal scrolling testimonial container on mobile */
            .testimonial-grid {
                display: flex;
                gap: 16px;
                width: 100%;
                overflow-x: auto;
                overflow-y: hidden;
                scroll-behavior: smooth;
                -webkit-overflow-scrolling: touch;
                padding: 0;
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* Internet Explorer 10+ */
            }

            /* Hide scrollbar for webkit browsers */
            .testimonial-grid::-webkit-scrollbar {
                display: none;
            }

            /* Equal height and fixed width for all cards */
            .testimonial-grid .testimonial-card {
                height: 180px;
                width: 280px;
                min-width: 280px;
                flex-shrink: 0;
                display: flex;
                flex-direction: column;
                overflow: hidden;
            }

            /* Header section */
            .testimonial-grid .testimonial-card .bg-gray-50\/80 {
                flex-shrink: 0;
                padding: 14px 18px !important;
                text-align: left;
            }

            /* Content section - fills remaining space */
            .testimonial-grid .testimonial-card .px-4.py-4 {
                flex: 1;
                display: flex;
                align-items: flex-start;
                padding: 18px !important;
                text-align: left;
            }

            /* Text sizing for readability */
            .testimonial-grid .testimonial-card h4 {
                font-size: 0.8rem !important;
                line-height: 1.2 !important;
                margin-bottom: 4px !important;
                text-align: left !important;
            }

            .testimonial-grid .testimonial-card .text-xs {
                font-size: 0.7rem !important;
                line-height: 1.2 !important;
                text-align: left !important;
            }

            .testimonial-grid .testimonial-card .text-sm {
                font-size: 0.75rem !important;
                line-height: 1.3 !important;
                text-align: left !important;
            }
        }
    </style>
</head>
<body class="smooth-scroll text-charcoal font-aeonik" style="background-color: #fffffb;">

    <!-- Mobile Header Section -->
    <header class="relative min-h-screen lg:min-h-[55vh] xl:min-h-[50vh] overflow-hidden lg:bg-none" style="background-image: url('./image/bus.png'); background-size: cover; background-position: center -300px; background-repeat: no-repeat;">
        <!-- Mobile background only -->
        <style>
            @media (min-width: 1024px) {
                header {
                    background-image: none !important;
                }
            }
        </style>
        <!-- Gradient Overlay -->
        <div class="absolute inset-0 z-0" style="background: linear-gradient(to bottom, #fffffb 0%, #fffffb 30%, rgba(255, 255, 251, 0.7) 50%, rgba(255, 255, 251, 0.3) 70%, rgba(255, 255, 251, 0.5) 90%);"></div>
        <!-- Faded Image Overlay -->
        <div class="absolute inset-0 z-5" style="background-color: rgba(255, 255, 251, 0.2);"></div>


        <!-- Hero Content -->
        <div class="relative z-10 px-6 pt-4">
            <!-- Mobile Layout (single column, centered) -->
            <div class="lg:hidden flex flex-col items-center text-center">
                <!-- Main Headline -->
                <h1 class="font-extrabold text-charcoal mb-6 font-league-gothic max-w-4xl mx-auto" style="line-height: 0.9; font-size: clamp(2.6rem, 9.2vw, 9.2rem);">
                    EXPERT<br>
                    <span class="font-black hero-headline-bold font-league-gothic" style="background: linear-gradient(145deg, #00a475, #008a63); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">BUS ACCIDENT</span><br>
                    <span class="font-extrabold attorneys-underline font-league-gothic">ATTORNEYS.</span>
                </h1>
                
                <!-- Subtext -->
                <p class="text-base text-gray-600 mb-8 max-w-2xl font-light leading-snug font-roboto-condensed">
                    We've guided thousands of bus accident victims recover by helping them reclaim their health, finances, and future.
                </p>
                
                <!-- CTA Button -->
                <button class="photorealistic-button menu-button text-white px-10 py-5 text-xl font-medium tracking-wide flex items-center space-x-3 mb-3 font-roboto-condensed relative z-10">
                    <span>Start Your Claim</span>
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </button>

                <!-- Shadcn-style Feature Bubble -->
                <div class="inline-flex items-center gap-2 px-4 py-2 mb-8 bg-white/90 backdrop-blur-sm border border-gray-200/60 rounded-full text-sm font-medium text-gray-700" style="box-shadow: 0 0 40px 20px rgba(255, 255, 255, 0.4), 0 8px 25px rgba(0, 0, 0, 0.15);">
                    <svg class="w-4 h-4 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <circle cx="12" cy="12" r="10"/>
                        <polyline points="12,6 12,12 16,14"/>
                    </svg>
                    <span>30 Second Sign Up</span>
                </div>

                <!-- Testimonial Grid - 2 Column Layout -->
                <div class="testimonial-grid mb-16">
                <!-- Card 1 -->
                <div class="testimonial-card bg-white/95 backdrop-blur-sm border border-gray-200/30 rounded-xl overflow-hidden">
                    <div class="bg-gray-50/80 px-4 py-3 border-b border-gray-200/50">
                        <div class="flex items-center">
                            <div>
                                <h4 class="font-medium text-gray-900 font-roboto-condensed text-sm">Sarah M</h4>
                                <div class="flex items-center">
                                    <svg class="w-3 h-3 text-primary-green mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    <p class="text-xs text-gray-500 font-light">Verified</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-4">
                        <p class="text-sm text-gray-700 leading-relaxed font-roboto-condensed font-light">
                            Outstanding service! They recovered $180k for my accident and made the process incredibly smooth.
                        </p>
                    </div>
                </div>

                <!-- Card 2 -->
                <div class="testimonial-card bg-white/95 backdrop-blur-sm border border-gray-200/30 rounded-xl overflow-hidden">
                    <div class="bg-gray-50/80 px-4 py-3 border-b border-gray-200/50">
                        <div class="flex items-center">
                            <div>
                                <h4 class="font-medium text-gray-900 font-roboto-condensed text-sm">Bryan H</h4>
                                <div class="flex items-center">
                                    <svg class="w-3 h-3 text-primary-green mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    <p class="text-xs text-gray-500 font-light">Verified Client</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-4">
                        <p class="text-sm text-gray-700 leading-relaxed font-roboto-condensed font-light">
                            My experience with Adamson Ahdoot was great. Maggie was amazing and took very good care of me throughout the entire process.
                        </p>
                    </div>
                </div>

                <!-- Card 3 -->
                <div class="testimonial-card bg-white/95 backdrop-blur-sm border border-gray-200/30 rounded-xl overflow-hidden">
                    <div class="bg-gray-50/80 px-4 py-3 border-b border-gray-200/50">
                        <div class="flex items-center">
                            <div>
                                <h4 class="font-medium text-gray-900 font-roboto-condensed text-sm">Lisa K</h4>
                                <div class="flex items-center">
                                    <svg class="w-3 h-3 text-primary-green mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    <p class="text-xs text-gray-500 font-light">Verified</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-4">
                        <p class="text-sm text-gray-700 leading-relaxed font-roboto-condensed font-light">
                            Incredible team! They fought hard for my case and got me the settlement I deserved. Highly recommend!
                        </p>
                    </div>
                </div>

                <!-- Card 4 -->
                <div class="testimonial-card bg-white/95 backdrop-blur-sm border border-gray-200/30 rounded-xl overflow-hidden">
                    <div class="bg-gray-50/80 px-4 py-3 border-b border-gray-200/50">
                        <div class="flex items-center">
                            <div>
                                <h4 class="font-medium text-gray-900 font-roboto-condensed text-sm">Mike R</h4>
                                <div class="flex items-center">
                                    <svg class="w-3 h-3 text-primary-green mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    <p class="text-xs text-gray-500 font-light">Verified Client</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-4 py-4">
                        <p class="text-sm text-gray-700 leading-relaxed font-roboto-condensed font-light">
                            Professional team. Communication was excellent and they made the whole process stress-free for my family.
                        </p>
                    </div>
                </div>
                </div>
            </div>

            <!-- Desktop Layout (two columns) -->
            <div class="hidden lg:grid lg:grid-cols-2 lg:gap-12 xl:gap-16 lg:items-center lg:min-h-[45vh]">
                <!-- Left Column - Content -->
                <div class="flex flex-col justify-center text-left">
                    <!-- Main Headline -->
                    <h1 class="font-extrabold text-charcoal mb-6 font-league-gothic" style="line-height: 0.9; font-size: clamp(2.2rem, 4vw, 4.5rem);">
                        EXPERT<br>
                        <span class="font-black hero-headline-bold font-league-gothic" style="background: linear-gradient(145deg, #00a475, #008a63); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">BUS ACCIDENT</span><br>
                        <span class="font-extrabold attorneys-underline font-league-gothic">ATTORNEYS.</span>
                    </h1>
                    
                    <!-- Subtext -->
                    <p class="text-lg text-gray-600 mb-8 max-w-xl font-light leading-relaxed font-roboto-condensed">
                        We've guided thousands of bus accident victims recover by helping them reclaim their health, finances, and future.
                    </p>
                    
                    <!-- CTA Button -->
                    <div class="flex items-center space-x-4 mb-6">
                        <button class="photorealistic-button menu-button text-white px-10 py-5 text-xl font-medium tracking-wide flex items-center space-x-3 font-roboto-condensed relative z-10">
                            <span>Start Your Claim</span>
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </button>
                        
                        <!-- Shadcn-style Feature Bubble -->
                        <div class="inline-flex items-center gap-2 px-4 py-2 bg-white/90 backdrop-blur-sm border border-gray-200/60 rounded-full text-sm font-medium text-gray-700" style="box-shadow: 0 0 40px 20px rgba(255, 255, 255, 0.4), 0 8px 25px rgba(0, 0, 0, 0.15);">
                            <svg class="w-4 h-4 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="10"/>
                                <polyline points="12,6 12,12 16,14"/>
                            </svg>
                            <span>30 Second Sign Up</span>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Image -->
                <div class="flex items-center justify-center">
                    <div class="relative w-full max-w-lg">
                        <img src="./image/bus.png" alt="Bus Accident Legal Representation" class="w-full h-auto object-cover rounded-2xl shadow-2xl" style="box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
                    </div>
                </div>
            </div>
        </div>




    </header>





    <script>
        // Smooth scroll for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>

</body>
</html>