<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AA.Law | Motor Vehicle Accident Attorney | Free Consultation | No Fees Unless We Win</title>
    <meta name="description" content="Injured in a motor vehicle accident? Get experienced legal help. Free consultation, no fees unless we win. Available 24/7 for accident victims.">

    <!-- Font Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Roboto+Condensed:wght@300;400;700&family=League+Gothic&display=swap" rel="stylesheet">

    <!-- Aeonik Font from CDN with specific weights -->
    <link href="https://fonts.cdnfonts.com/css/aeonik" rel="stylesheet">

    <!-- Additional Aeonik font loading to ensure Regular weight -->
    <style>
        @import url('https://fonts.cdnfonts.com/css/aeonik');

        @font-face {
            font-family: 'Aeonik';
            src: url('https://fonts.cdnfonts.com/s/19990/AeonikTRIAL-Regular.woff') format('woff');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
    </style>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'aeonik': ['Aeonik', 'Inter', 'SF Pro Display', 'Helvetica Neue', 'Arial', 'sans-serif'],
                        'roboto-condensed': ['Roboto Condensed', 'Arial Narrow', 'sans-serif'],
                        'league-gothic': ['League Gothic', 'Impact', 'Arial Narrow', 'sans-serif'],
                    },
                    colors: {
                        'primary-green': '#00a475',
                        'secondary-green': '#00c48c',
                        'vibrant-green': '#00e6a3',
                        'light-green': '#e6fff5',
                        'deep-black': '#000000',
                        'charcoal': '#1a1a1a',
                        'light-gray': '#f8f9fa',
                        'accent-orange': '#ff6b35',
                        'form-bg': '#ffffff',
                        'page-bg': '#fffffb',
                        'form-border': '#00a475',
                    },
                    spacing: {
                        '18': '4.5rem',
                        '22': '5.5rem',
                        '26': '6.5rem',
                        '30': '7.5rem',
                        '34': '8.5rem',
                        '38': '9.5rem',
                        '42': '10.5rem',
                        '46': '11.5rem',
                        '50': '12.5rem',
                        '128': '32rem',
                        '144': '36rem',
                        '160': '40rem',
                        '192': '48rem',
                    },
                    fontSize: {
                        '5xl': ['3rem', { lineHeight: '1.1' }],
                        '6xl': ['3.75rem', { lineHeight: '1.1' }],
                        '7xl': ['4.5rem', { lineHeight: '1.1' }],
                        '8xl': ['6rem', { lineHeight: '1.1' }],
                        '9xl': ['8rem', { lineHeight: '1.1' }],
                    },
                    maxWidth: {
                        '8xl': '90rem',
                        '9xl': '100rem',
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <style>
        * {
            font-family: 'Aeonik', 'Inter', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif !important;
            font-weight: 400 !important;
        }

        body, h1, h2, h3, h4, h5, h6, p, span, div, a, button {
            font-family: 'Aeonik', 'Inter', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif !important;
            font-weight: 400 !important;
        }

        .font-light {
            font-weight: 400 !important;
        }

        .font-normal {
            font-weight: 400 !important;
        }

        .font-medium {
            font-weight: 400 !important;
        }

        .font-semibold {
            font-weight: 400 !important;
        }

        .font-bold {
            font-weight: 400 !important;
        }

        .hero-headline-bold {
            font-weight: 900 !important;
        }

        .attorneys-underline {
            position: relative;
        }

        .attorneys-underline::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 120%;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, #00a475 20%, #008a63 50%, #00a475 80%, transparent 100%);
            border-radius: 1px;
            opacity: 0.8;
        }

        /* 3D Photorealistic Button */
        .photorealistic-button {
            background: linear-gradient(145deg, #008a63, #007355);
            border: 3px solid rgba(255, 255, 255, 0.4);
            border-radius: 50px;
            box-shadow: 
                0 8px 20px rgba(0, 0, 0, 0.15),
                0 4px 12px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                inset 0 -2px 0 rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
            color: #ffffff !important;
        }

        .photorealistic-button:hover {
            transform: translateY(-2px);
            background: linear-gradient(145deg, #009970, #008060);
            box-shadow: 
                0 12px 28px rgba(0, 0, 0, 0.2),
                0 6px 16px rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.4),
                inset 0 -2px 0 rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.3);
        }

        .photorealistic-button:active {
            transform: translateY(0px);
            background: linear-gradient(145deg, #007355, #006048);
            box-shadow: 
                0 4px 12px rgba(0, 0, 0, 0.2),
                0 2px 8px rgba(0, 0, 0, 0.15),
                inset 0 2px 4px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
        }

        .photorealistic-button::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            height: 50%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
            border-radius: 50px 50px 0 0;
            pointer-events: none;
        }

        .photorealistic-button::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
            transform: rotate(-45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .photorealistic-button:hover::after {
            opacity: 1;
            transform: rotate(-45deg) translate(50%, 50%);
        }

        .smooth-scroll {
            scroll-behavior: smooth;
        }

        /* Mobile to desktop responsive adjustments */
        @media (max-width: 768px) {
            .attorneys-underline::after {
                left: 50%;
                transform: translateX(-50%);
            }
        }

        @media (min-width: 1024px) {
            .attorneys-underline::after {
                left: 0;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body class="smooth-scroll text-charcoal font-aeonik" style="background-color: #fffffb;">

    <!-- Responsive Header Section -->
    <header class="relative min-h-screen overflow-hidden flex items-center justify-center lg:justify-start lg:min-h-[55vh] xl:min-h-[50vh]">
        <!-- Hero Content -->
        <div class="relative z-10 px-6 pt-4 max-w-6xl mx-auto">
            <!-- Content Layout - Centered on mobile, left-aligned on desktop -->
            <div class="flex flex-col text-center lg:text-left lg:max-w-2xl">
                <!-- Main Headline -->
                <h1 class="font-extrabold text-charcoal mb-6 font-league-gothic" style="line-height: 0.9; font-size: clamp(2.34rem, 8.28vw, 4.05rem);">
                    EXPERT<br>
                    <span class="font-black hero-headline-bold font-league-gothic" style="background: linear-gradient(145deg, #00a475, #008a63); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">BUS ACCIDENT</span><br>
                    <span class="font-extrabold attorneys-underline font-league-gothic">ATTORNEYS.</span>
                </h1>
                
                <!-- Subtext -->
                <p class="text-base lg:text-lg text-gray-600 mb-8 max-w-2xl lg:max-w-xl font-light leading-snug lg:leading-relaxed font-roboto-condensed mx-auto lg:mx-0">
                    We've guided thousands of bus accident victims recover by helping them reclaim their health, finances, and future.
                </p>
                
                <!-- CTA Section -->
                <div class="flex flex-col items-start space-y-4 mb-6">
                    <!-- CTA Button -->
                    <button class="photorealistic-button text-white px-10 py-5 text-xl font-medium tracking-wide flex items-center space-x-3 font-roboto-condensed relative z-10">
                        <span>Start Your Claim</span>
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </button>
                    
                    <!-- Shadcn-style Feature Bubble -->
                    <div class="inline-flex items-center gap-2 px-4 py-2 bg-white/90 backdrop-blur-sm border border-gray-200/60 rounded-full text-sm font-medium text-gray-700" style="box-shadow: 0 0 40px 20px rgba(255, 255, 255, 0.4), 0 8px 25px rgba(0, 0, 0, 0.15);">
                        <svg class="w-4 h-4 text-primary-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10"/>
                            <polyline points="12,6 12,12 16,14"/>
                        </svg>
                        <span>30 Second Sign Up</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <script>
        // Smooth scroll for internal links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>

</body>
</html> 