<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Bakery Team</title>

    <!-- Font Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Roboto+Condensed:wght@300;400;700&family=League+Gothic&display=swap" rel="stylesheet">

    <!-- Aeonik Font from CDN with specific weights -->
    <link href="https://fonts.cdnfonts.com/css/aeonik" rel="stylesheet">

    <!-- Additional Aeonik font loading to ensure Regular weight -->
    <style>
        @import url('https://fonts.cdnfonts.com/css/aeonik');

        @font-face {
            font-family: 'Aeonik';
            src: url('https://fonts.cdnfonts.com/s/19990/AeonikTRIAL-Regular.woff') format('woff');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }
    </style>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'aeonik': ['Aeonik', 'Inter', 'SF Pro Display', 'Helvetica Neue', 'Arial', 'sans-serif'],
                        'roboto-condensed': ['Roboto Condensed', 'Arial Narrow', 'sans-serif'],
                        'league-gothic': ['League Gothic', 'Impact', 'Arial Narrow', 'sans-serif'],
                    },
                    colors: {
                        'primary-green': '#00a475',
                        'secondary-green': '#00c48c',
                        'vibrant-green': '#00e6a3',
                        'light-green': '#e6fff5',
                        'deep-black': '#000000',
                        'charcoal': '#1a1a1a',
                        'light-gray': '#f8f9fa',
                        'accent-orange': '#ff6b35',
                        'form-bg': '#ffffff',
                        'page-bg': '#fffffb',
                        'form-border': '#00a475',
                    },
                }
            }
        }
    </script>

    <style>
        * {
            font-family: 'Aeonik', 'Inter', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif !important;
        }

        body {
            background-color: #000000;
            color: #ffffff;
            margin: 0;
            padding: 0;
        }

        .team-section {
            min-height: 100vh;
            padding: 80px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .team-title {
            font-size: 4rem;
            font-weight: 300;
            text-align: center;
            margin-bottom: 1.5rem;
            color: #ffffff;
        }

        .team-title .italic-word {
            font-style: italic;
            font-weight: 400;
        }

        .team-subtitle {
            font-size: 1.1rem;
            color: #999999;
            text-align: center;
            margin-bottom: 4rem;
            max-width: 600px;
            line-height: 1.6;
        }

        .team-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            width: 100%;
        }

        .team-card {
            background-color: #1a1a1a;
            border-radius: 16px;
            overflow: hidden;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .team-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .team-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            display: block;
        }

        .team-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8) 20%, rgba(0, 0, 0, 0.95) 100%);
            padding: 1rem 2rem 1rem;
            color: #ffffff;
        }

        .team-name {
            font-size: 1.5rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .team-role {
            font-size: 1rem;
            color: #00a475;
            font-style: italic;
            margin-bottom: 0.5rem;
        }

        .team-description {
            font-size: 0.9rem;
            color: #cccccc;
            line-height: 1.4;
            margin-bottom: 0.75rem;
        }

        .social-icons {
            display: flex;
            gap: 1rem;
        }

        .social-icon {
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease, transform 0.3s ease;
            cursor: pointer;
        }

        .social-icon:hover {
            background-color: #00a475;
            transform: scale(1.1);
        }

        .social-icon svg {
            width: 18px;
            height: 18px;
            fill: #ffffff;
        }

        @media (max-width: 768px) {
            .team-title {
                font-size: 2.5rem;
            }

            .team-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .team-section {
                padding: 60px 15px;
            }
        }
    </style>
</head>
<body>
    <section class="team-section">
        <!-- Team Title -->
        <h2 class="team-title">
            The <span class="italic-word">Bakery</span> Team
        </h2>

        <!-- Team Subtitle -->
        <p class="team-subtitle">
            Urna, in est malesuada arcu massa posuere convallis. Id tristique sed velit tellus lorem pellentesque id ut ultrices.
        </p>

        <!-- Team Grid -->
        <div class="team-grid">
            <!-- Team Member 1 -->
            <div class="team-card">
                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" alt="Cymone Magdalina" class="team-image">
                <div class="team-content">
                    <h3 class="team-name">Cymone Magdalina</h3>
                    <p class="team-role">Chef</p>
                    <p class="team-description">
                        Sed vitae egestas maecenas arcu ultrices ipsum suspendisse amet.
                    </p>
                    <div class="social-icons">
                        <div class="social-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                            </svg>
                        </div>
                        <div class="social-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Member 2 -->
            <div class="team-card">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80" alt="Sophia Ella" class="team-image">
                <div class="team-content">
                    <h3 class="team-name">Sophia Ella</h3>
                    <p class="team-role">Professional Baker</p>
                    <p class="team-description">
                        Sed vitae egestas maecenas arcu ultrices ipsum suspendisse amet.
                    </p>
                    <div class="social-icons">
                        <div class="social-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                            </svg>
                        </div>
                        <div class="social-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Member 3 -->
            <div class="team-card">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80" alt="Hyeon Vivek" class="team-image">
                <div class="team-content">
                    <h3 class="team-name">Hyeon Vivek</h3>
                    <p class="team-role">Cooking Teacher</p>
                    <p class="team-description">
                        Sed vitae egestas maecenas arcu ultrices ipsum suspendisse amet.
                    </p>
                    <div class="social-icons">
                        <div class="social-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                            </svg>
                        </div>
                        <div class="social-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>